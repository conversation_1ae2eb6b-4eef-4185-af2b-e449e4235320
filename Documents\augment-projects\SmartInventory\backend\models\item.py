"""
Item model for Smart Inventory Management System

This module defines the Pydantic models for inventory items.
Designed for small Indian businesses to manage their stock efficiently.
"""

from typing import Optional
from pydantic import BaseModel, Field


class ItemBase(BaseModel):
    """Base model for inventory items with common fields"""
    name: str = Field(..., min_length=1, max_length=100, description="Name of the inventory item")
    quantity: int = Field(..., ge=0, description="Quantity in stock (must be non-negative)")
    price: float = Field(..., gt=0, description="Price per unit (must be positive)")


class ItemCreate(ItemBase):
    """Model for creating new inventory items"""
    pass


class ItemUpdate(BaseModel):
    """Model for updating existing inventory items (all fields optional)"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Name of the inventory item")
    quantity: Optional[int] = Field(None, ge=0, description="Quantity in stock (must be non-negative)")
    price: Optional[float] = Field(None, gt=0, description="Price per unit (must be positive)")


class Item(ItemBase):
    """Complete item model with ID for responses"""
    id: int = Field(..., description="Unique identifier for the inventory item")
    
    class Config:
        """Pydantic configuration"""
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "name": "Basmati Rice 1kg",
                "quantity": 50,
                "price": 120.0
            }
        }


class ItemResponse(BaseModel):
    """Response model for API operations"""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Human-readable message about the operation")
    data: Optional[Item] = Field(None, description="Item data if applicable")


class ItemListResponse(BaseModel):
    """Response model for listing multiple items"""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Human-readable message about the operation")
    data: list[Item] = Field(..., description="List of inventory items")
    total_items: int = Field(..., description="Total number of items in inventory")
    total_value: float = Field(..., description="Total value of all inventory items")
