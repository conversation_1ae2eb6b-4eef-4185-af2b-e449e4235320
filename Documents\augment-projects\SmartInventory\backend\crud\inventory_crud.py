"""
CRUD operations for Smart Inventory Management System

This module handles all database operations for inventory items.
Uses in-memory storage for simplicity, suitable for small businesses.
"""

from typing import List, Optional
from models.item import Item, ItemCreate, ItemUpdate


class InventoryStorage:
    """In-memory storage for inventory items"""
    
    def __init__(self):
        self.items: List[Item] = []
        self.next_id: int = 1
        
        # Add some sample data for demonstration
        self._add_sample_data()
    
    def _add_sample_data(self):
        """Add sample inventory items for Indian businesses"""
        sample_items = [
            {"name": "Basmati Rice 1kg", "quantity": 50, "price": 120.0},
            {"name": "Toor Dal 500g", "quantity": 30, "price": 85.0},
            {"name": "Sunflower Oil 1L", "quantity": 25, "price": 140.0},
            {"name": "Wheat Flour 1kg", "quantity": 40, "price": 45.0},
            {"name": "Sugar 1kg", "quantity": 35, "price": 55.0},
        ]
        
        for item_data in sample_items:
            item = Item(id=self.next_id, **item_data)
            self.items.append(item)
            self.next_id += 1


# Global storage instance
storage = InventoryStorage()


def create_item(item_data: ItemCreate) -> Item:
    """
    Create a new inventory item
    
    Args:
        item_data: Item data to create
        
    Returns:
        Created item with assigned ID
    """
    new_item = Item(
        id=storage.next_id,
        name=item_data.name,
        quantity=item_data.quantity,
        price=item_data.price
    )
    
    storage.items.append(new_item)
    storage.next_id += 1
    
    return new_item


def get_all_items() -> List[Item]:
    """
    Get all inventory items
    
    Returns:
        List of all items in inventory
    """
    return storage.items.copy()


def get_item_by_id(item_id: int) -> Optional[Item]:
    """
    Get a specific item by ID
    
    Args:
        item_id: ID of the item to retrieve
        
    Returns:
        Item if found, None otherwise
    """
    for item in storage.items:
        if item.id == item_id:
            return item
    return None


def update_item(item_id: int, item_data: ItemUpdate) -> Optional[Item]:
    """
    Update an existing inventory item
    
    Args:
        item_id: ID of the item to update
        item_data: Updated item data
        
    Returns:
        Updated item if found, None otherwise
    """
    for i, item in enumerate(storage.items):
        if item.id == item_id:
            # Update only provided fields
            update_data = item_data.model_dump(exclude_unset=True)
            
            updated_item = Item(
                id=item.id,
                name=update_data.get("name", item.name),
                quantity=update_data.get("quantity", item.quantity),
                price=update_data.get("price", item.price)
            )
            
            storage.items[i] = updated_item
            return updated_item
    
    return None


def delete_item(item_id: int) -> bool:
    """
    Delete an inventory item
    
    Args:
        item_id: ID of the item to delete
        
    Returns:
        True if item was deleted, False if not found
    """
    for i, item in enumerate(storage.items):
        if item.id == item_id:
            storage.items.pop(i)
            return True
    return False


def get_inventory_stats() -> dict:
    """
    Get inventory statistics
    
    Returns:
        Dictionary with total items, total value, and low stock alerts
    """
    total_items = len(storage.items)
    total_value = sum(item.quantity * item.price for item in storage.items)
    low_stock_items = [item for item in storage.items if item.quantity < 10]
    
    return {
        "total_items": total_items,
        "total_value": round(total_value, 2),
        "low_stock_count": len(low_stock_items),
        "low_stock_items": low_stock_items
    }


def search_items(query: str) -> List[Item]:
    """
    Search items by name
    
    Args:
        query: Search query string
        
    Returns:
        List of items matching the search query
    """
    query_lower = query.lower()
    return [
        item for item in storage.items 
        if query_lower in item.name.lower()
    ]
