# Smart Inventory Management System - Backend Dependencies
# Designed for small Indian businesses

# Core FastAPI framework
fastapi==0.104.1

# ASGI server for running FastAPI
uvicorn[standard]==0.24.0

# Data validation and serialization
pydantic==2.5.0

# Type hints support for older Python versions
typing-extensions==4.8.0

# Optional: For future database integration
# sqlalchemy==2.0.23
# alembic==1.12.1

# Optional: For authentication (future enhancement)
# python-jose[cryptography]==3.3.0
# passlib[bcrypt]==1.7.4
# python-multipart==0.0.6

# Optional: For testing
# pytest==7.4.3
# httpx==0.25.2

# Optional: For enhanced logging
# python-json-logger==2.0.7

# Optional: For configuration management
# python-dotenv==1.0.0

# Development dependencies (uncomment if needed)
# black==23.11.0
# flake8==6.1.0
# mypy==1.7.1
